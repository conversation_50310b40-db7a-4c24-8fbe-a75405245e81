import Tracker from '@openreplay/tracker';

export const tracker = new Tracker({
  projectKey: import.meta.env.VITE_OPENREPLAY_PROJECT_KEY!,
  __DISABLE_SECURE_MODE: import.meta.env.NODE_ENV === 'development',
  network: {
    failuresOnly: false,
    sessionTokenHeader: false,
    ignoreHeaders: false,
    capturePayload: true,
    captureInIframes: true,
  },
  defaultInputMode: 0,
  obscureInputNumbers: false,
  obscureInputEmails: false,
  obscureTextEmails: false,
});
