import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { Label } from 'src/lib/components/ui/label';
import { Input } from 'src/lib/components/ui/input';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { ChangeEvent, useMemo, useState } from 'react';
import { AutocompleteInput } from 'src/lib/components/ui/autocomplete';

type SingleLineFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function SingleLineField({ field, participantIndex }: SingleLineFieldProps) {
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    if (registrationEntries && registrationEntries.length > participantIndex) {
      return registrationEntries[participantIndex].fields[field.key]
        ? String(registrationEntries[participantIndex].fields[field.key])
        : '';
    }

    return '';
  }, [field.key, registrationEntries, participantIndex]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    updateFieldValue(participantIndex, field.key, e.target.value);
  };

  const handleChangeOnBlur = (value: string) => {
    setTouched(true);
    const trimmedValue = value.trim();
    updateFieldValue(participantIndex, field.key, trimmedValue);
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;

  if (field.values && field.values.length > 0) {
    return (
      <div className="space-y-2" key={field.key}>
        <Label htmlFor={`${participantIndex}-${field.key}`} className="flex items-center">
          {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        <div className={showError ? 'border-destructive' : ''}>
          <AutocompleteInput
            id={`${participantIndex}-${field.key}`}
            suggestions={field.values}
            placeholder={field.placeholder}
            defaultValue={currentValue}
            onChange={value => {
              updateFieldValue(participantIndex, field.key, value);
            }}
            onBlur={value => {
              handleChangeOnBlur(value);
            }}
            className={showError ? 'border-destructive' : ''}
          />
        </div>
        {showError && <p className="text-sm text-destructive">{error?.message}</p>}
        {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
      </div>
    );
  }

  return (
    <div className="space-y-2" key={field.key}>
      <Label htmlFor={`${participantIndex}-${field.key}`} className="flex items-center">
        {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      <Input
        id={`${participantIndex}-${field.key}`}
        value={currentValue}
        name={field.key}
        onChange={handleChange}
        onBlur={e => handleChangeOnBlur(e.target.value)}
        placeholder={field.placeholder}
        maxLength={field.maxLength}
        className={showError ? 'border-destructive' : ''}
      />
      {showError && <p className="text-sm text-destructive">{error?.message}</p>}
      {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
    </div>
  );
}
