{"forms.billing.address-line": "Address Line", "forms.billing.billing-method": "Billing Method", "forms.billing.billing-method-email": "E-Mail", "forms.billing.billing-method-letter": "Letter", "forms.billing.city": "City", "forms.billing.country": "Country", "forms.billing.email": "E-Mail", "forms.billing.first-name": "Firstname", "forms.billing.last-name": "Lastname", "forms.billing.title": "Billing Information", "forms.billing.intro": "Please check your billing information and adapt it if needed:", "forms.billing.zipCode": "Zip Code", "forms.event-not-found.message": "Event not Found. Please provide a valid event id.", "forms.form-not-found.message": "Form not Found. Please provide a valid event id.", "forms.inactive-form.message": "Inactive Form", "forms.inactive-form.status-closed": "The registration window is closed. Currently no further registrations will be accepted.", "forms.inactive-form.status-full": "The event is completely booked. No further places can be booked.", "forms.inactive-form.status-wait": "The login window is not open yet. It opens on", "forms.inactive-form.status-waiting-list": "All already available places are occupied. However, it is possible to join the waiting list.", "forms.error-form.title": "We're sorry, an error has occurred.", "forms.common.title": "Registration for {{event.name}}", "forms.initial-questionnaire.title": "Registration", "forms.initial-questionnaire.instructions": "Please select who you would like to register", "forms.initial-questionnaire.selection.myself": "Myself", "forms.initial-questionnaire.selection.myself.description": "Here you register as an <1>individual</1>.", "forms.initial-questionnaire.selection.my-family": "My Family", "forms.initial-questionnaire.selection.my-family.description": "Here you register yourself and your family or your spouse (<1>two persons or more</1>). The persons must all live in the same household.", "forms.initial-questionnaire.selection.family-members": "Family Members / <1>Kids</1>", "forms.initial-questionnaire.selection.family-members.description": "This is where you register your children, but not yourself.", "forms.initial-questionnaire.selection.myself-and-others": "<PERSON> and others", "forms.initial-questionnaire.selection.myself-and-others.description": "Here you register yourself and other people who do not live in the same household as you.", "forms.initial-questionnaire.selection.leader": "Leader", "forms.initial-questionnaire.selection.leader.description": "Here you register yourself if you are part of the team.", "forms.event-info.title": "Event Information", "forms.event-info.event": "Event", "forms.event-info.date": "Date", "forms.event-info.time": "Time", "forms.event-info.organizer": "Organizer", "forms.event-info.registration-deadline": "Registration Deadline", "forms.event-info.available-seats": "Available Seats", "forms.event-info.waiting-list": "Waiting List", "forms.common.loading": "Loading...", "forms.common.back": "Back", "forms.common.submit": "Submit", "forms.common.incorrect-field-pattern": "The input should have the following form: {{pattern}}", "forms.common.incorrect-field-pattern.country": "Please enter a valid country.", "forms.common.incorrect-field-pattern.generic": "Please enter a valid value.", "forms.common.incorrect-field-pattern.required": "This Field is required.", "forms.common.incorrect-field-pattern.email": "Please enter a valid email address.", "forms.common.incorrect-field-pattern.max-length": "Please enter a value with a maximum length of {{maxLength}}.", "forms.common.incorrect-field-pattern.mobile": "Please enter a valid mobile number.", "forms.common.incorrect-field-pattern.zip-code": "Please enter a valid zip code.", "forms.common.technical-details": "Technical Details", "forms.multi-form.title": "Multiple Registrations", "forms.multi-form.additional-participant": "Additional Participant", "forms.multi-form.submit": "Submit", "forms.multi-form.delete-dialog.title": "Do you really want to delete this participant?", "forms.multi-form.delete-dialog.description": "Deleting this participant cannot be undone.", "forms.multi-form.delete-dialog.abort": "Abort", "forms.multi-form.delete-dialog.submit": "Delete", "forms.registrant.title": "Information about you", "forms.registrant.title.family-members": "Information of the guardian person", "forms.registrant.description.family-members": "Here you enter your data so that we can contact you directly for information or queries.\nYou yourself are not logged in.", "forms.common.next": "Next", "forms.registrant.first-name": "Firstname", "forms.registrant.last-name": "Lastname", "forms.registrant.email": "Email", "forms.registrant.mobile": "Mobile", "forms.single-form.title": "Individual form", "forms.single-form.submit": "Submit", "forms.stepper.billing-info": "Billing Information", "forms.stepper.info": "Event Information", "forms.stepper.identification": "Identification", "forms.stepper.registration-form": "Registration Form", "forms.stepper.registration-confirmation": "Confirmation", "forms.submission-failed.message": "Unfortunately an error occurred during the submission. Please try again later.", "forms.successful-submission.title": "Application almost completed", "form.toggleable-registration.new-participant": "New Participant", "field.terms-and-conditions.accept-the": "I accept the ", "error-page.event-not-found.title": "Event Not Found", "error-page.event-not-found.description": "We could not find the requested event.", "error-page.form-not-found.title": "Form Not Found", "error-page.form-not-found.description": "The registration form for this event is not available.", "error-page.technical-error.title": "Technical Error", "error-page.technical-error.description": "An unexpected technical error occurred.", "error-page.default.title": "Error", "error-page.default.description": "An unexpected error occurred.", "error-page.no-event-id.title": "No Event ID", "error-page.no-event-id.description": "No event ID was provided", "forms.no-event-id.message": "Please provide an event ID to view the registration form.", "event-info.date": "Date", "event-info.time": "Time", "event-info.organizer": "Organizer", "event-info.registration-deadline": "Registration Deadline", "event-info.available-seats": "Available Seats", "event-info.waiting-list-spots": "Waiting list: {{count}} spots available", "event-info.available-spots": "{{available}} of {{total}} spots available", "event-info.age-restriction": "Age Restriction", "event-info.age-range": "{{min}} to {{max}} years old", "event-info.min-age": "Minimum age: {{age}} years", "event-info.max-age": "Maximum age: {{age}} years", "event-info.age-restricted": "Age restricted", "event-info.birth-year-range": "Birth year between {{min}} and {{max}}", "event-info.min-birth-year": "Minimum birth year: {{year}}", "event-info.max-birth-year": "Maximum birth year: {{year}}", "field.single-select.placeholder": "Select an option...", "field.single-select.search": "Search {{name}}...", "field.single-select.no-options": "No option found.", "validations.required": "This field is required.", "validations.email": "Please check your email address.", "validations.mobile": "Please check your phone number.", "validations.max-length": "The input must be at most {{maxLength}} characters long.", "validations.pattern": "The input must have the following form: {{pattern}}", "validations.required-selection": "At least one option must be selected.", "validations.date": "Please enter a valid date.", "validations.min-age": "You are too young to participate. (You must be at least {{date}} years old)", "validations.max-age": "You are too old to participate. (You must be at most {{date}} years old)", "validations.min-age-year": "You are too young to participate. (Birthdate must be before {{date}})", "validations.max-age-year": "You are too old to participate. (Birthdate must be after {{date}})", "validations.min-options": "Please select at least {{count}} options.", "validations.max-options": "Please select at most {{count}} options.", "validations.date-of-birth-future": "Date of birth cannot be in the future.", "registration-type.title": "Registration Type", "registration-type.participant": "Participant", "registration-type.leader": "Leader", "welcome-page.title": "Registration for {{eventName}}", "stepper.event-details": "Event details", "stepper.participants": "Participants", "stepper.emergency-contact": "Emergency contact", "stepper.parental-responsibility": "Parental responsibility", "stepper.registrant": "Registrant", "stepper.billing": "Billing Information", "stepper.confirmation": "Confirmation", "stepper.step-count": "Step {{current}} of {{total}}", "shared.navigation.back": "Back", "shared.navigation.next": "Next", "shared.navigation.cancel": "Cancel", "shared.navigation.delete": "Delete", "registrant.title": "Registrant Information", "registrant.description": "Who is completing this registration?", "registrant.participants": "Participants", "registrant.someone-else": "Someone else", "registrant.first-name": "First Name", "registrant.last-name": "Last Name", "contact.add-another": "Add Another Contact", "contact.delete.title": "Delete Contact", "contact.delete.description": "Are you sure you want to delete this contact? This action cannot be undone.", "date-picker.placeholder": "Pick a date", "date-picker.year": "Year", "shared.form-status.wait.title": "Registration not yet open", "shared.form-status.wait.description": "The registration window opens on {{date}}.", "shared.form-status.private.title": "Private Registration", "shared.form-status.private.description": "This registration is private. Please enter the registration code to continue.", "shared.form-status.waiting-list.title": "Waiting List", "shared.form-status.waiting-list.description": "All available places are occupied. However, you can join the waiting list.", "shared.form-status.full.title": "Event Full", "shared.form-status.full.description": "The event is completely booked. No further places can be booked.", "shared.form-status.closed.title": "Registration Closed", "shared.form-status.closed.description": "The registration window is closed. Currently, no further registrations will be accepted.", "page.participants.title": "Participant Details", "page.participants.description": "Please provide details for each participant", "page.participants.add-another": "Add Another Participant", "page.participants.delete.title": "Delete Participant", "page.participants.delete.description": "Are you sure you want to delete this participant? This action cannot be undone.", "page.participants.delete.cancel": "Cancel", "page.participants.delete.confirm": "Delete", "page.billing.title": "Billing Address", "page.billing.description": "Please provide your billing information", "page.billing.first-name": "First Name", "page.billing.last-name": "Last Name", "page.billing.address": "Address", "page.billing.zip-code": "Zip Code", "page.billing.city": "City", "page.billing.country": "Country", "page.billing.billing-method": "Billing Method", "page.billing.country-placeholder": "Select a country", "page.billing.method-email": "Email", "page.billing.method-letter": "Letter", "page.billing.email": "Email", "page.billing.submit": "Submit Form", "page.billing.error.first-name-required": "First name is required", "page.billing.error.last-name-required": "Last name is required", "page.billing.error.address-required": "Address is required", "page.billing.error.zip-code-required": "Zip code is required", "page.billing.error.zip-code-format": "Zip code must be 4-5 digits", "page.billing.error.city-required": "City is required", "page.billing.error.country-required": "Country is required", "page.billing.error.email-required": "Email is required", "page.billing.error.email-format": "Please enter a valid email address", "page.confirmation.loading": "Loading...", "page.confirmation.failed": "Submission Failed", "page.confirmation.successful": "Submission Successful", "page.private-form.registration-code": "Registration Code", "page.private-form.placeholder": "Enter registration code", "page.private-form.submit": "Submit", "page.private-form.error.code-required": "Please enter a registration code", "page.private-form.error.event-not-found": "Event ID not found", "page.contact.validation-error": "Please fill in all required fields before proceeding", "page.contact.error.type-required": "Contact type is required", "page.contact.error.first-name-required": "First name is required", "page.contact.error.last-name-required": "Last name is required", "page.contact.error.mobile-required": "Phone number is required", "page.contact.error.mobile-format": "Please enter a valid phone number", "page.contact.error.email-format": "Please enter a valid email address", "page.registrants.error.first-name-required": "First name is required", "page.registrants.error.last-name-required": "Last name is required", "page.registrants.error.email-required": "Email is required", "page.registrants.error.email-format": "Please enter a valid email address", "page.registrants.error.mobile-required": "Phone number is required", "page.registrants.error.mobile-format": "Please enter a valid phone number", "registrant.parent-only-message": "When registering underage participants, only parent/guardian contacts can be selected as the registrant.", "page.contact.type.PARENT": "Parent/Guardian", "page.contact.type.PARTNER": "Partner", "page.contact.type.GRAND_PARENT": "Grand Parent", "page.contact.type.RELATIVE": "Relative", "page.contact.type.WELL_KNOWN": "Well Known", "page.contact.type.placeholder": "Select contact type", "page.contact.label.type": "Type", "page.contact.label.first-name": "First Name", "page.contact.label.last-name": "Last Name", "page.contact.label.phone": "Phone Number", "page.contact.label.email": "Email", "page.contact.title.emergency": "Emergency Contact", "page.contact.title.parental": "Parental Responsibility", "page.contact.description.emergency": "Please provide emergency contact information", "page.contact.description.parental": "Please provide contact information for the parent or guardian responsible for the underage participant(s)", "page.contact.number": "Contact {{count}}", "page.contact.parent-only-message": "When registering underage participants, only parent/guardian contacts are allowed. Additional contacts cannot be added."}