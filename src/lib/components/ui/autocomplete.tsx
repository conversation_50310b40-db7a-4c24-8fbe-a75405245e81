import * as React from 'react';
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from 'src/lib/components/ui/command';
import { Input } from 'src/lib/components/ui/input';
import { cn } from 'src/lib/utils';

interface AutocompleteInputProps {
  suggestions: string[];
  placeholder?: string;
  className?: string;
  onChange?: (value: string) => void;
  onBlur?: (value: string) => void;
  defaultValue?: string;
  id?: string;
}

export function AutocompleteInput({
  suggestions,
  placeholder,
  className,
  onChange,
  onBlur,
  defaultValue = '',
  id,
}: AutocompleteInputProps) {
  const [value, setValue] = React.useState(defaultValue);
  const [showSuggestions, setShowSuggestions] = React.useState(false);
  const [highlightedIndex, setHighlightedIndex] = React.useState(-1);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const commandRef = React.useRef<HTMLDivElement>(null);

  const filteredSuggestions = React.useMemo(() => {
    if (!value) return suggestions;

    return suggestions.filter(suggestion => suggestion.toLowerCase().includes(value.toLowerCase()));
  }, [value, suggestions]);

  // Handle clicks outside to close suggestions
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        commandRef.current &&
        !commandRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    setHighlightedIndex(-1);

    if (onChange) {
      onChange(newValue);
    }

    const newFilteredSuggestions = !newValue
      ? suggestions
      : suggestions.filter(suggestion => suggestion.toLowerCase().includes(newValue.toLowerCase()));

    if (newFilteredSuggestions.length > 0) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionSelect = (selectedValue: string) => {
    setValue(selectedValue);
    if (onChange) {
      onChange(selectedValue);
    }
    inputRef.current?.focus();
    setShowSuggestions(false);
    setHighlightedIndex(-1);
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (commandRef.current?.contains(e.relatedTarget as Node)) {
      return;
    }
    setShowSuggestions(false);
    setHighlightedIndex(-1);

    const trimmedValue = value.trim();
    setValue(trimmedValue);

    if (onBlur) {
      onBlur(value);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions) {
      return;
    }

    const currentFilteredSuggestions = !value
      ? suggestions
      : suggestions.filter(suggestion => suggestion.toLowerCase().includes(value.toLowerCase()));

    if (currentFilteredSuggestions.length === 0) {
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => (prev < currentFilteredSuggestions.length - 1 ? prev + 1 : 0));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => (prev > 0 ? prev - 1 : currentFilteredSuggestions.length - 1));
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < currentFilteredSuggestions.length) {
          handleSuggestionSelect(currentFilteredSuggestions[highlightedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  return (
    <div className="relative w-full">
      <Input
        ref={inputRef}
        id={id}
        value={value}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={cn('w-full', className)}
        onFocus={() => {
          if (filteredSuggestions.length > 0) {
            setShowSuggestions(true);
            setHighlightedIndex(-1);
          }
        }}
      />

      {showSuggestions && (
        <div
          ref={commandRef}
          className="absolute z-50 w-full mt-1 overflow-hidden bg-popover text-popover-foreground rounded-md border shadow-md"
        >
          {/* Override existing arrow navigation of cmd input to avoid conflicting behaviour */}
          <Command className="w-full" value="" onValueChange={() => {}}>
            <CommandList>
              <CommandEmpty></CommandEmpty>
              <CommandGroup>
                {filteredSuggestions.map((suggestion, index) => (
                  <CommandItem
                    key={suggestion}
                    value={suggestion}
                    onSelect={() => handleSuggestionSelect(suggestion)}
                    className={cn('cursor-pointer', index === highlightedIndex && 'bg-accent text-accent-foreground')}
                  >
                    {suggestion}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
}
