/**
 * Utility functions for country code conversion
 */

// Mapping of country names to ISO 3166-1 alpha-2 codes
const COUNTRY_NAME_TO_ALPHA2_MAP: Record<string, string> = {
  // German country names
  'Schweiz': 'CH',
  'Deutschland': 'DE',
  'Österreich': 'AT',
  'Frankreich': 'FR',
  'Italien': 'IT',
  'Spanien': 'ES',
  'Niederlande': 'NL',
  'Belgien': 'BE',
  'Luxemburg': 'LU',
  'Polen': 'PL',
  'Tschechien': 'CZ',
  'Ungarn': 'HU',
  'Slowakei': 'SK',
  'Slowenien': 'SI',
  'Kroatien': 'HR',
  'Serbien': 'RS',
  'Bosnien und Herzegowina': 'BA',
  'Montenegro': 'ME',
  'Nordmazedonien': 'MK',
  'Albanien': 'AL',
  'Bulgarien': 'BG',
  'Rumänien': 'RO',
  'Griechenland': 'G<PERSON>',
  'Türk<PERSON>': 'TR',
  '<PERSON>yper<PERSON>': 'CY',
  '<PERSON><PERSON>': 'IE',
  'Vereinigtes Königreich': 'GB',
  'Norwegen': 'NO',
  'Schweden': 'SE',
  'Dänemark': 'DK',
  'Finnland': 'FI',
  'Island': 'IS',
  'Estland': 'EE',
  'Lettland': 'LV',
  'Litauen': 'LT',
  'Russland': 'RU',
  'Moldau': 'MD',
  
  // English country names
  'Switzerland': 'CH',
  'Germany': 'DE',
  'Austria': 'AT',
  'France': 'FR',
  'Italy': 'IT',
  'Spain': 'ES',
  'Netherlands': 'NL',
  'Belgium': 'BE',
  'Luxembourg': 'LU',
  'Poland': 'PL',
  'Czech Republic': 'CZ',
  'Hungary': 'HU',
  'Slovakia': 'SK',
  'Slovenia': 'SI',
  'Croatia': 'HR',
  'Serbia': 'RS',
  'Bosnia and Herzegovina': 'BA',
  'North Macedonia': 'MK',
  'Albania': 'AL',
  'Bulgaria': 'BG',
  'Romania': 'RO',
  'Greece': 'GR',
  'Turkey': 'TR',
  'Cyprus': 'CY',
  'Malta (EN)': 'MT',
  'Portugal (EN)': 'PT',
  'Ireland': 'IE',
  'United Kingdom': 'GB',
  'Norway': 'NO',
  'Sweden': 'SE',
  'Denmark': 'DK',
  'Finland': 'FI',
  'Iceland': 'IS',
  'Estonia': 'EE',
  'Latvia': 'LV',
  'Lithuania': 'LT',
  'Russia': 'RU',
  'Ukraine': 'UA',
  'Belarus': 'BY',
  'Moldova': 'MD',
};

/**
 * Converts a country name to its ISO 3166-1 alpha-2 code
 * @param countryName The country name (in German or English)
 * @returns The alpha-2 country code or the original value if no mapping found
 */
export function convertCountryNameToAlpha2(countryName: string): string {
  // If the value is already a 2-letter alpha-2 code, return it as is
  if (/^[A-Z]{2}$/.test(countryName)) {
    return countryName;
  }
  
  // Try to find the mapping
  const alpha2Code = COUNTRY_NAME_TO_ALPHA2_MAP[countryName];
  
  if (alpha2Code) {
    return alpha2Code;
  }
  
  // If no direct match, try case-insensitive search
  const lowerCaseCountryName = countryName.toLowerCase();
  for (const [name, code] of Object.entries(COUNTRY_NAME_TO_ALPHA2_MAP)) {
    if (name.toLowerCase() === lowerCaseCountryName) {
      return code;
    }
  }
  
  // Return original value if no mapping found
  return countryName;
}

/**
 * Validates if a country code is a valid ISO 3166-1 alpha-2 code
 * @param countryCode The country code to validate
 * @returns True if valid alpha-2 code, false otherwise
 */
export function isValidAlpha2CountryCode(countryCode: string): boolean {
  return /^[A-Z]{2}$/.test(countryCode);
}