import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { EventRegistrationContactType } from 'src/generated/api/dsv-public/model';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from 'src/lib/components/ui/card';
import { Label } from 'src/lib/components/ui/label';
import { RadioGroup, RadioGroupItem } from 'src/lib/components/ui/radio-group';
import { FormNavigation } from 'src/shared/form-navigation';
import { FormStep, useFormProgressStore } from 'src/stores/form-progress-store';
import { Input } from 'src/lib/components/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { phoneFieldSchema } from 'src/validators/phone-field-schema';
import { useTranslation } from 'react-i18next';
import { useFormContext } from 'src/context/form-context';
import { Separator } from 'src/lib/components/ui/separator';
import { isUnderage } from 'src/utils/date-utils';

const registrantSchema = (t: any) =>
  z.object({
    firstName: z
      .string()
      .min(1, t('page.registrants.error.first-name-required'))
      .max(32, t('validations.max-length', { maxLength: 32 })),
    lastName: z
      .string()
      .min(1, t('page.registrants.error.last-name-required'))
      .max(32, t('validations.max-length', { maxLength: 32 })),
    email: z
      .string()
      .min(1, t('page.registrants.error.email-required'))
      .email(t('page.registrants.error.email-format')),
    mobile: phoneFieldSchema({ required: true, maxLength: 32 }),
  });

type RegistrantFormValues = z.infer<ReturnType<typeof registrantSchema>>;

export function RegistrantPage() {
  const { t } = useTranslation();
  const { availableSteps, submitForm, form, hasUnderageParticipant } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const contacts = useFormProgressStore(state => state.contacts);
  const registrant = useFormProgressStore(state => state.registrant);
  const setRegistrant = useFormProgressStore(state => state.setRegistrant);
  const isUnderageForm = hasUnderageParticipant && form.underageCheckEnabled;

  const getOriginalParticipantIndex = useCallback(
    (participant: any) => {
      return registrationEntries.findIndex(
        entry =>
          String(entry.fields.firstName) === String(participant?.firstName) &&
          String(entry.fields.lastName) === String(participant?.lastName) &&
          String(entry.fields.email) === String(participant?.email) &&
          String(entry.fields.mobile) === String(participant?.mobile)
      );
    },
    [registrationEntries]
  );

  const getOriginalContactIndex = useCallback(
    (contact: any) => {
      return contacts.findIndex(
        c =>
          String(c.firstName) === String(contact?.firstName) &&
          String(c.lastName) === String(contact?.lastName) &&
          String(c.email) === String(contact?.email) &&
          String(c.mobile) === String(contact?.mobile)
      );
    },
    [contacts]
  );

  const registrantMatchIndex = useMemo(() => {
    const index = getOriginalParticipantIndex(registrant);
    if (index !== -1) return `participant_${index}`;

    const contactIndex = getOriginalContactIndex(registrant);
    if (contactIndex !== -1) return `contact_${contactIndex}`;

    return 'other';
  }, [registrant, getOriginalContactIndex, getOriginalParticipantIndex]);

  const {
    reset,
    register,
    formState: { errors },
    setValue,
    trigger,
  } = useForm<RegistrantFormValues>({
    mode: 'onTouched',
    resolver: zodResolver(registrantSchema(t)),
    defaultValues: registrant,
  });

  const updateFieldOnChange = (field: keyof RegistrantFormValues, value: string) => {
    const trimmedValue = value.trim();
    // @ts-ignore
    setRegistrant({ ...registrant, [field]: trimmedValue });
    setValue(field, value);
  };

  const updateFieldOnBlur = async (field: keyof RegistrantFormValues, value: string) => {
    const trimmedValue = value.trim();
    // @ts-ignore
    setRegistrant({ ...registrant, [field]: trimmedValue });
    setValue(field, trimmedValue);
    await trigger(field);
  };

  const registrantOptions = useMemo(() => {
    return registrationEntries
      .filter(entry => {
        if (!entry.fields.email || !entry.fields.mobile) {
          return false;
        }

        if (form.underageCheckEnabled && entry.fields.dateOfBirth) {
          return !isUnderage(String(entry.fields.dateOfBirth));
        }
        return true;
      })
      .map(entry => {
        return {
          firstName: entry.fields.firstName,
          lastName: entry.fields.lastName,
          email: entry.fields.email,
          mobile: entry.fields.mobile,
        };
      });
  }, [registrationEntries, form.underageCheckEnabled]);

  const filteredContacts = useMemo(() => {
    const contactsWithRequiredFields = contacts.filter(contact => contact.email && contact.mobile);

    if (isUnderageForm) {
      return contactsWithRequiredFields.filter(contact => contact.type === EventRegistrationContactType.PARENT);
    }
    return contactsWithRequiredFields;
  }, [contacts, isUnderageForm]);

  const hasNoOptions = registrantOptions.length === 0 && filteredContacts.length === 0;
  const [selectedOption, setSelectedOption] = useState<string>(hasNoOptions ? 'other' : registrantMatchIndex);

  const handleOptionChange = (value: string) => {
    setSelectedOption(value);
    if (value === 'other') {
      setRegistrant({
        firstName: undefined,
        lastName: undefined,
        email: '',
        mobile: undefined,
      });
      reset({
        firstName: undefined,
        lastName: undefined,
        email: '',
        mobile: undefined,
      });
      return;
    }

    if (value.startsWith('participant_')) {
      const index = parseInt(value.split('_')[1]);
      const participant = registrantOptions[index];
      setRegistrant({
        firstName: String(participant.firstName),
        lastName: String(participant.lastName),
        email: String(participant.email),
        mobile: participant.mobile ? String(participant.mobile) : undefined,
      });
    } else if (value.startsWith('contact_')) {
      const index = parseInt(value.split('_')[1]);
      const contact = contacts[index];
      setRegistrant({
        firstName: String(contact.firstName),
        lastName: String(contact.lastName),
        email: String(contact.email),
        mobile: contact.mobile ? String(contact.mobile) : undefined,
      });
    }
  };

  const areRequiredFieldsFilled = useMemo(() => {
    return registrant && Object.values(registrant).every(field => !!field);
  }, [registrant]);

  const handleNext = async () => {
    if (!availableSteps.includes(FormStep.BILLING)) {
      await submitForm();
    }

    return true;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t('registrant.title')}</CardTitle>
        <CardDescription>{t('registrant.description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <RadioGroup value={selectedOption} onValueChange={handleOptionChange}>
            {registrantOptions.length > 0 && (
              <>
                {registrantOptions.map(participant => {
                  const originalIndex = getOriginalParticipantIndex(participant);
                  return (
                    <Label
                      key={`participant-${originalIndex}`}
                      htmlFor={`participant-${originalIndex}`}
                      className="flex items-start space-x-2 p-3 border rounded-md cursor-pointer"
                    >
                      <RadioGroupItem value={`participant_${originalIndex}`} id={`participant-${originalIndex}`} />
                      <div className="grid gap-1">
                        <div className="font-medium">
                          {String(participant.firstName)} {String(participant.lastName)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div>{participant.email ? String(participant.email) : ''}</div>
                          <div>{participant.mobile ? String(participant.mobile) : ''}</div>
                        </div>
                      </div>
                    </Label>
                  );
                })}
              </>
            )}

            {filteredContacts.length > 0 && (
              <>
                {filteredContacts.map(contact => {
                  const originalIndex = getOriginalContactIndex(contact);
                  return (
                    <Label
                      key={`contact-${originalIndex}`}
                      htmlFor={`contact-${originalIndex}`}
                      className="flex items-start space-x-2 p-3 border rounded-md cursor-pointer"
                    >
                      <RadioGroupItem value={`contact_${originalIndex}`} id={`contact-${originalIndex}`} />
                      <div className="grid gap-1">
                        <div className="font-medium">
                          {String(contact.firstName)} {String(contact.lastName)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div>{contact.email ? String(contact.email) : ''}</div>
                          <div>{contact.mobile ? String(contact.mobile) : ''}</div>
                        </div>
                      </div>
                    </Label>
                  );
                })}
              </>
            )}

            {(!isUnderageForm || hasNoOptions) && (
              <>
                {!hasNoOptions && <Separator className="my-1" />}
                <Label htmlFor="other" className="flex items-start space-x-3 p-3 border rounded-md cursor-pointer">
                  <RadioGroupItem value="other" id="other" />
                  <div className="font-medium">{t('registrant.someone-else')}</div>
                </Label>
              </>
            )}
          </RadioGroup>

          {selectedOption === 'other' && (
            <div className="space-y-4 mt-4 p-4 border rounded-md">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="flex items-center">
                  {t('registrant.first-name')} <span className="text-destructive ml-1">*</span>
                </Label>
                <Input
                  id="firstName"
                  defaultValue={registrant?.firstName}
                  {...register('firstName')}
                  onChange={e => updateFieldOnChange('firstName', e.target.value)}
                  onBlur={e => updateFieldOnBlur('firstName', e.target.value)}
                  className={errors.firstName ? 'border-destructive' : ''}
                />
                {errors.firstName && <p className="text-sm text-destructive">{errors.firstName.message}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="flex items-center">
                  {t('registrant.last-name')} <span className="text-destructive ml-1">*</span>
                </Label>
                <Input
                  id="lastName"
                  defaultValue={registrant?.lastName}
                  {...register('lastName')}
                  onChange={e => updateFieldOnChange('lastName', e.target.value)}
                  onBlur={e => updateFieldOnBlur('lastName', e.target.value)}
                  className={errors.lastName ? 'border-destructive' : ''}
                />
                {errors.lastName && <p className="text-sm text-destructive">{errors.lastName.message}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center">
                  Email <span className="text-destructive ml-1">*</span>
                </Label>
                <Input
                  id="email"
                  type="email"
                  defaultValue={registrant?.email}
                  {...register('email')}
                  onChange={e => updateFieldOnChange('email', e.target.value)}
                  onBlur={e => updateFieldOnBlur('email', e.target.value)}
                  className={errors.email ? 'border-destructive' : ''}
                />
                {errors.email && <p className="text-sm text-destructive">{errors.email.message}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="mobile" className="flex items-center">
                  Phone Number <span className="text-destructive ml-1">*</span>
                </Label>
                <Input
                  id="mobile"
                  type="tel"
                  defaultValue={registrant?.mobile}
                  {...register('mobile')}
                  onChange={e => updateFieldOnChange('mobile', e.target.value)}
                  onBlur={e => updateFieldOnBlur('mobile', e.target.value)}
                  className={errors.mobile ? 'border-destructive' : ''}
                />
                {errors.mobile && <p className="text-sm text-destructive">{errors.mobile.message}</p>}
              </div>
            </div>
          )}
        </div>
        <FormNavigation onNext={handleNext} nextDisabled={!areRequiredFieldsFilled || Object.keys(errors).length > 0} />
      </CardContent>
    </Card>
  );
}
