import { Card, CardContent, CardDescription, CardHeader, CardTitle } from 'src/lib/components/ui/card';
import { Input } from 'src/lib/components/ui/input';
import { Label } from 'src/lib/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from 'src/lib/components/ui/select';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useEffect, useMemo } from 'react';
import { FormNavigation } from 'src/shared/form-navigation';
import { cn } from 'src/lib/utils';
import { EventRegistrationBillingMethod, I18Texts } from 'src/generated/api/dsv-public/model';
import { useFormContext } from 'src/context/form-context';
import { useFormStore } from 'src/stores/form-store';
import { useTranslation } from 'react-i18next';

const getBillingSchema = (t: (key: string) => string, billingMethod?: EventRegistrationBillingMethod) =>
  z.object({
    firstName: z
      .string({
        message: t('page.billing.error.first-name-required'),
      })
      .min(1, t('page.billing.error.first-name-required')),
    lastName: z
      .string({
        message: t('page.billing.error.last-name-required'),
      })
      .min(1, t('page.billing.error.last-name-required')),
    addressLine: z
      .string({
        message: t('page.billing.error.address-required'),
      })
      .min(1, t('page.billing.error.address-required')),
    zipCode: z
      .string({
        message: t('page.billing.error.zip-code-required'),
      })
      .min(1, t('page.billing.error.zip-code-required'))
      .regex(/^[0-9]{4,5}$/, t('page.billing.error.zip-code-format')),
    city: z
      .string({
        message: t('page.billing.error.city-required'),
      })
      .min(1, t('page.billing.error.city-required')),
    country: z
      .string({
        message: t('page.billing.error.country-required'),
      })
      .min(1, t('page.billing.error.country-required')),
    email:
      billingMethod === EventRegistrationBillingMethod.EMAIL
        ? z
            .string({
              message: t('page.billing.error.email-required'),
            })
            .min(1, t('page.billing.error.email-required'))
            .email(t('page.billing.error.email-format'))
        : z.string().optional(),
  });

const billingSchema = getBillingSchema(key => key);

type BillingFormValues = z.infer<typeof billingSchema>;

export function BillingPage() {
  const { t, i18n } = useTranslation();
  const { submitForm } = useFormContext();
  const billingCountries = useFormStore(state => state.billingCountries);
  const initBillingCountries = useFormStore(state => state.initBillingCountries);
  const billingAddress = useFormProgressStore(state => state.billingAddress);
  const setBillingAddress = useFormProgressStore(state => state.setBillingAddress);
  const billingMethod = useFormProgressStore(state => state.billingMethod);
  const setBillingMethod = useFormProgressStore(state => state.setBillingMethod);
  const registrant = useFormProgressStore(state => state.registrant);

  useEffect(() => {
    if (!billingCountries) {
      void initBillingCountries();
    }
  }, [billingCountries, initBillingCountries]);

  const translatedBillingSchema = useMemo(() => getBillingSchema(t, billingMethod), [t, billingMethod]);

  const {
    clearErrors,
    register,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<BillingFormValues>({
    mode: 'onTouched',
    resolver: zodResolver(translatedBillingSchema),
    defaultValues: {
      ...billingAddress,
      email:
        billingAddress?.email ||
        (billingMethod === EventRegistrationBillingMethod.EMAIL ? registrant?.email || '' : ''),
    },
  });

  const updateFieldOnChange = (field: keyof BillingFormValues, value: string) => {
    // @ts-ignore
    setBillingAddress({ ...billingAddress, [field]: value });
    setValue(field, value);
  };

  const updateFieldOnBlur = async (field: keyof BillingFormValues, value: string) => {
    const trimmedValue = value.trim();
    // @ts-ignore
    setBillingAddress({ ...billingAddress, [field]: trimmedValue });
    setValue(field, trimmedValue);
    if (Object.keys(errors).length > 0) {
      console.log('Form errors:', Object.keys(errors));
    }
    await trigger(field);
  };

  const updateBillingMethod = (method: string) => {
    setBillingMethod(method as EventRegistrationBillingMethod);

    if (method === EventRegistrationBillingMethod.EMAIL && registrant?.email) {
      updateFieldOnChange('email', registrant.email);
    }

    if (method === EventRegistrationBillingMethod.LETTER) {
      updateFieldOnChange('email', '');
      clearErrors('email');
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t('page.billing.title')}</CardTitle>
        <CardDescription>{t('page.billing.description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="firstName" className="flex items-center">
            {t('page.billing.first-name')} <span className="text-destructive ml-1">*</span>
          </Label>
          <Input
            id="firstName"
            {...register('firstName')}
            defaultValue={billingAddress?.firstName}
            onChange={e => updateFieldOnChange('firstName', e.target.value)}
            onBlur={e => updateFieldOnBlur('firstName', e.target.value)}
            className={errors.firstName ? 'border-destructive' : ''}
          />
          {errors.firstName && <p className="text-sm text-destructive">{errors.firstName.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastName" className="flex items-center">
            {t('page.billing.last-name')} <span className="text-destructive ml-1">*</span>
          </Label>
          <Input
            id="lastName"
            {...register('lastName')}
            defaultValue={billingAddress?.lastName}
            onChange={e => updateFieldOnChange('lastName', e.target.value)}
            onBlur={e => updateFieldOnBlur('lastName', e.target.value)}
            className={errors.lastName ? 'border-destructive' : ''}
          />
          {errors.lastName && <p className="text-sm text-destructive">{errors.lastName.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="address" className="flex items-center">
            {t('page.billing.address')} <span className="text-destructive ml-1">*</span>
          </Label>
          <Input
            id="addressLine"
            {...register('addressLine')}
            defaultValue={billingAddress?.addressLine}
            onChange={e => updateFieldOnChange('addressLine', e.target.value)}
            onBlur={e => updateFieldOnBlur('addressLine', e.target.value)}
            className={errors.addressLine ? 'border-destructive' : ''}
          />
          {errors.addressLine && <p className="text-sm text-destructive">{errors.addressLine.message}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="zipCode" className="flex items-center">
              {t('page.billing.zip-code')} <span className="text-destructive ml-1">*</span>
            </Label>
            <Input
              id="zipCode"
              {...register('zipCode')}
              defaultValue={billingAddress?.zipCode}
              onChange={e => updateFieldOnChange('zipCode', e.target.value)}
              onBlur={e => updateFieldOnBlur('zipCode', e.target.value)}
              className={errors.zipCode ? 'border-destructive' : ''}
            />
            {errors.zipCode && <p className="text-sm text-destructive">{errors.zipCode.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="city" className="flex items-center">
              {t('page.billing.city')} <span className="text-destructive ml-1">*</span>
            </Label>
            <Input
              id="city"
              {...register('city')}
              defaultValue={billingAddress?.city}
              onChange={e => updateFieldOnChange('city', e.target.value)}
              onBlur={e => updateFieldOnBlur('city', e.target.value)}
              className={errors.city ? 'border-destructive' : ''}
            />
            {errors.city && <p className="text-sm text-destructive">{errors.city.message}</p>}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="country" className="flex items-center">
            {t('page.billing.country')} <span className="text-destructive ml-1">*</span>
          </Label>
          <Select defaultValue={billingAddress?.country} onValueChange={value => updateFieldOnChange('country', value)}>
            <SelectTrigger id="country" className={cn(errors.country && 'border-destructive', 'w-full')}>
              <SelectValue placeholder={t('page.billing.country-placeholder')} />
            </SelectTrigger>
            <SelectContent>
              {billingCountries?.map(country => (
                <SelectItem key={country.value} value={country.value}>
                  {country.name[i18n.language as keyof I18Texts]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.country && <p className="text-sm text-destructive">{errors.country.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="billing-method" className="flex items-center">
            {t('page.billing.billing-method')} <span className="text-destructive ml-1">*</span>
          </Label>
          <Select value={billingMethod} onValueChange={updateBillingMethod}>
            <SelectTrigger id="billing-method" className="w-full">
              <SelectValue defaultValue={EventRegistrationBillingMethod.EMAIL} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={EventRegistrationBillingMethod.EMAIL}>{t('page.billing.method-email')}</SelectItem>
              <SelectItem value={EventRegistrationBillingMethod.LETTER}>{t('page.billing.method-letter')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {billingMethod === EventRegistrationBillingMethod.EMAIL && (
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center">
              {t('page.billing.email')} <span className="text-destructive ml-1">*</span>
            </Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              defaultValue={registrant?.email || ''}
              onChange={e => updateFieldOnChange('email', e.target.value)}
              onBlur={e => updateFieldOnBlur('email', e.target.value)}
              className={errors.email ? 'border-destructive' : ''}
            />
            {errors.email && <p className="text-sm text-destructive">{errors.email.message}</p>}
          </div>
        )}

        <FormNavigation
          nextText={t('page.billing.submit')!}
          nextDisabled={!!Object.keys(errors).length}
          onNext={submitForm}
        />
      </CardContent>
    </Card>
  );
}
